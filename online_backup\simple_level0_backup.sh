#!/bin/bash

# 简化的Oracle Level 0备份脚本 - 解决TAG长度和验证问题
# 功能：执行基本的Level 0增量备份，避免复杂的验证步骤

# 加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 备份配置
BACKUP_DATE=$(date +%Y%m%d)
BACKUP_TIME=$(date +%H%M%S)
BACKUP_TIMESTAMP="${BACKUP_DATE}_${BACKUP_TIME}"
LOGFILE="$BACKUP_LOG_DIR/simple_level0_backup_$BACKUP_TIMESTAMP.log"

# 简化的备份标签（确保不超过31字符）
BACKUP_TAG="L0_$BACKUP_DATE"
DB_FORMAT="$BACKUP_BASE_DIR/level0/l0_%d_%T_%U.bkp"
ARCH_FORMAT="$BACKUP_BASE_DIR/archivelogs/arc_%d_%T_%U.arc"
CTL_FORMAT="$BACKUP_BASE_DIR/controlfiles/ctl_%d_%T.ctl"

# 创建备份目录
mkdir -p "$BACKUP_BASE_DIR/level0"
mkdir -p "$BACKUP_BASE_DIR/archivelogs"
mkdir -p "$BACKUP_BASE_DIR/controlfiles"
mkdir -p "$BACKUP_LOG_DIR"

# 日志函数
log_backup() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

log_backup_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOGFILE" >&2
}

log_backup_success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" | tee -a "$LOGFILE"
}

# 检查前置条件
check_prerequisites() {
    log_backup "检查备份前置条件..."
    
    # 检查数据库状态
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)
    
    if [ "$db_status" != "OPEN" ]; then
        log_backup_error "数据库状态异常: $db_status"
        return 1
    fi
    
    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)
    
    if [ "$archive_mode" != "ARCHIVELOG" ]; then
        log_backup_error "数据库未启用归档模式: $archive_mode"
        return 1
    fi
    
    log_backup_success "前置条件检查通过"
    return 0
}

# 执行简化的Level 0备份
execute_simple_backup() {
    log_backup "========================================="
    log_backup "开始执行简化的Level 0备份"
    log_backup "备份标签: $BACKUP_TAG"
    log_backup "========================================="
    
    # 执行RMAN备份
    rman target / log="$LOGFILE" <<EOF
# 基本配置
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE CONTROLFILE AUTOBACKUP ON;

# 清理过期备份
CROSSCHECK BACKUP;
DELETE NOPROMPT EXPIRED BACKUP;

# 执行Level 0备份（启用压缩）
BACKUP AS COMPRESSED BACKUPSET INCREMENTAL LEVEL 0 DATABASE FORMAT '$DB_FORMAT' TAG '$BACKUP_TAG';

# 备份归档日志（启用压缩）
BACKUP AS COMPRESSED BACKUPSET ARCHIVELOG ALL FORMAT '$ARCH_FORMAT' TAG '${BACKUP_TAG}_ARC' DELETE INPUT;

# 备份控制文件
BACKUP CURRENT CONTROLFILE FORMAT '$CTL_FORMAT' TAG '${BACKUP_TAG}_CTL';

# 列出备份信息
LIST BACKUP SUMMARY;

EXIT;
EOF
    
    local rman_exit_code=$?
    
    if [ $rman_exit_code -eq 0 ]; then
        log_backup_success "Level 0备份执行完成"
        return 0
    else
        log_backup_error "Level 0备份执行失败，退出代码: $rman_exit_code"
        return 1
    fi
}

# 生成备份报告
generate_simple_report() {
    log_backup "生成备份报告..."
    
    local report_file="$BACKUP_LOG_DIR/simple_level0_report_$BACKUP_TIMESTAMP.txt"
    
    cat > "$report_file" <<EOF
简化Level 0备份报告
==================
备份时间: $BACKUP_TIMESTAMP
备份标签: $BACKUP_TAG
Oracle SID: $ORACLE_SID

备份文件位置:
- 数据库备份: $BACKUP_BASE_DIR/level0/
- 归档日志: $BACKUP_BASE_DIR/archivelogs/
- 控制文件: $BACKUP_BASE_DIR/controlfiles/

备份文件列表:
EOF
    
    # 添加文件列表
    find "$BACKUP_BASE_DIR" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null
    
    echo -e "\n备份完成时间: $(date)" >> "$report_file"
    echo -e "详细日志: $LOGFILE" >> "$report_file"
    
    log_backup "备份报告已生成: $report_file"
}

# 主函数
main() {
    log_backup "========================================="
    log_backup "简化Oracle Level 0备份开始"
    log_backup "开始时间: $(date)"
    log_backup "日志文件: $LOGFILE"
    log_backup "========================================="
    
    local backup_start_time=$(date +%s)
    
    # 检查前置条件
    if ! check_prerequisites; then
        log_backup_error "前置条件检查失败"
        exit 1
    fi
    
    # 执行备份
    if ! execute_simple_backup; then
        log_backup_error "备份执行失败"
        exit 1
    fi
    
    # 生成报告
    generate_simple_report
    
    # 计算耗时
    local backup_end_time=$(date +%s)
    local backup_duration=$((backup_end_time - backup_start_time))
    local backup_duration_formatted=$(printf "%02d:%02d:%02d" $((backup_duration/3600)) $((backup_duration%3600/60)) $((backup_duration%60)))
    
    log_backup "========================================="
    log_backup_success "简化Level 0备份成功完成"
    log_backup "完成时间: $(date)"
    log_backup "备份耗时: $backup_duration_formatted"
    log_backup "备份位置: $BACKUP_BASE_DIR"
    log_backup "========================================="
    
    echo "========================================="
    echo "✅ 简化Level 0备份成功完成！"
    echo "备份耗时: $backup_duration_formatted"
    echo "详细日志: $LOGFILE"
    echo "备份位置: $BACKUP_BASE_DIR"
    echo "========================================="
}

# 执行主函数
main "$@"
